#!/usr/bin/env python3
"""
Milvus知识库管理脚本
功能：增加、删除、查看知识库集合
作者：AI Assistant
"""
import os
import sys
from dotenv import load_dotenv
from typing import List, Optional

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取ai_rag_system目录 (当前目录的上上级)
ai_rag_dir = os.path.dirname(os.path.dirname(current_dir))

# 加载环境变量
env_path = os.path.join(ai_rag_dir, '.env')
load_dotenv(env_path)

try:
    from pymilvus import MilvusClient
except ImportError:
    print("❌ 错误：请先安装pymilvus库")
    print("   运行命令：pip install pymilvus")
    sys.exit(1)

class MilvusKBManager:
    """Milvus知识库管理器"""
    
    def __init__(self):
        """初始化连接"""
        self.uri = os.getenv('MILVUS_URI')
        if not self.uri:
            print("❌ 错误：未找到MILVUS_URI环境变量")
            print(f"   请检查 {env_path} 文件")
            sys.exit(1)
        
        try:
            self.client = MilvusClient(uri=self.uri)
            print(f"✅ 成功连接到Milvus: {self.uri}")
        except Exception as e:
            print(f"❌ 连接Milvus失败: {e}")
            sys.exit(1)
    
    def list_collections(self) -> List[str]:
        """列出所有知识库集合"""
        try:
            collections = self.client.list_collections()
            return collections
        except Exception as e:
            print(f"❌ 获取集合列表失败: {e}")
            return []
    
    def create_collection(self, name: str, description: str = "") -> bool:
        """创建新的知识库集合"""
        try:
            # 检查集合名称是否有效
            if not self._is_valid_collection_name(name):
                print(f"❌ 无效的集合名称: {name}")
                print("   集合名称必须以字母或下划线开头，只能包含字母、数字和下划线")
                return False
            
            # 检查集合是否已存在
            collections = self.list_collections()
            if name in collections:
                print(f"⚠️  集合 '{name}' 已存在")
                return False
            
            # 创建集合
            self.client.create_collection(
                collection_name=name,
                dimension=1536,  # OpenAI embedding维度
                metric_type="COSINE",
                consistency_level="Strong"
            )
            
            print(f"✅ 成功创建知识库: {name}")
            if description:
                print(f"   描述: {description}")
            return True
            
        except Exception as e:
            print(f"❌ 创建集合失败: {e}")
            return False
    
    def delete_collection_by_index(self, indices: list) -> bool:
        """通过序号删除知识库集合（支持批量删除）"""
        try:
            collections = self.list_collections()
            if not collections:
                print("📭 当前没有任何知识库")
                return False
            
            # 验证序号有效性
            valid_indices = []
            invalid_indices = []
            to_delete = []
            
            for idx in indices:
                if 1 <= idx <= len(collections):
                    valid_indices.append(idx)
                    to_delete.append(collections[idx - 1])
                else:
                    invalid_indices.append(idx)
            
            if invalid_indices:
                print(f"⚠️  无效序号: {invalid_indices}")
                print(f"   有效范围: 1-{len(collections)}")
            
            if not valid_indices:
                print("❌ 没有有效的序号")
                return False
            
            # 显示将要删除的知识库
            print(f"\n📋 将要删除以下知识库:")
            for idx in valid_indices:
                print(f"  {idx}. {collections[idx - 1]}")
            
            # 确认删除
            if len(valid_indices) == 1:
                confirm_msg = f"⚠️  确定要删除知识库 '{to_delete[0]}' 吗？(y/N): "
            else:
                confirm_msg = f"⚠️  确定要删除这 {len(valid_indices)} 个知识库吗？(y/N): "
            
            confirm = input(confirm_msg).strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 取消删除操作")
                return False
            
            # 执行删除
            success_count = 0
            for name in to_delete:
                try:
                    self.client.drop_collection(collection_name=name)
                    print(f"✅ 成功删除: {name}")
                    success_count += 1
                except Exception as e:
                    print(f"❌ 删除 '{name}' 失败: {e}")
            
            print(f"\n🎉 批量删除完成，成功删除 {success_count}/{len(to_delete)} 个知识库")
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 批量删除失败: {e}")
            return False

    def rename_collection(self, old_name: str, new_name: str) -> bool:
        """重命名知识库集合（通过复制数据实现）"""
        try:
            collections = self.list_collections()

            # 检查源集合是否存在
            if old_name not in collections:
                print(f"⚠️  源知识库 '{old_name}' 不存在")
                return False

            # 检查新名称是否有效
            if not self._is_valid_collection_name(new_name):
                print(f"❌ 无效的新名称: {new_name}")
                print("   集合名称必须以字母或下划线开头，只能包含字母、数字和下划线")
                return False

            # 检查新名称是否已存在
            if new_name in collections:
                print(f"⚠️  目标名称 '{new_name}' 已存在")
                return False

            print(f"🔄 开始重命名: {old_name} -> {new_name}")

            # 获取源集合的数据
            try:
                # 查询所有数据
                search_results = self.client.query(
                    collection_name=old_name,
                    filter="",
                    output_fields=["*"],
                    limit=10000  # 限制查询数量，实际应用中可能需要分批处理
                )

                print(f"📊 找到 {len(search_results)} 条数据")

            except Exception as e:
                print(f"⚠️  源集合可能为空或查询失败: {e}")
                search_results = []

            # 创建新集合
            self.client.create_collection(
                collection_name=new_name,
                dimension=1536,
                metric_type="COSINE",
                consistency_level="Strong"
            )
            print(f"✅ 创建新集合: {new_name}")

            # 如果有数据，复制到新集合
            if search_results:
                try:
                    self.client.insert(collection_name=new_name, data=search_results)
                    print(f"✅ 数据复制完成: {len(search_results)} 条")
                except Exception as e:
                    print(f"⚠️  数据复制失败: {e}")
                    print("   新集合已创建，但数据复制失败")

            # 删除原集合
            confirm = input(f"⚠️  是否删除原集合 '{old_name}'？(y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                self.client.drop_collection(collection_name=old_name)
                print(f"✅ 删除原集合: {old_name}")
                print(f"🎉 重命名完成: {old_name} -> {new_name}")
            else:
                print(f"⚠️  保留原集合 '{old_name}'，现在有两个集合")
                print(f"✅ 新集合创建完成: {new_name}")

            return True

        except Exception as e:
            print(f"❌ 重命名失败: {e}")
            return False

    def get_collection_info(self, name: str) -> Optional[dict]:
        """获取集合详细信息"""
        try:
            collections = self.list_collections()
            if name not in collections:
                print(f"⚠️  集合 '{name}' 不存在")
                return None

            # 获取集合统计信息
            stats = self.client.get_collection_stats(collection_name=name)
            return {
                'name': name,
                'stats': stats
            }

        except Exception as e:
            print(f"❌ 获取集合信息失败: {e}")
            return None

    def _is_valid_collection_name(self, name: str) -> bool:
        """检查集合名称是否有效"""
        if not name:
            return False

        # 第一个字符必须是字母或下划线
        if not (name[0].isalpha() or name[0] == '_'):
            return False

        # 只能包含字母、数字和下划线
        for char in name:
            if not (char.isalnum() or char == '_'):
                return False

        return True

    def display_collections(self, show_stats=False):
        """显示所有集合"""
        collections = self.list_collections()

        if not collections:
            print("📭 当前没有任何知识库集合")
            return collections

        print(f"\n📚 当前知识库列表 (共{len(collections)}个):")
        print("-" * 60)
        for i, collection in enumerate(collections, 1):
            if show_stats:
                try:
                    stats = self.client.get_collection_stats(collection_name=collection)
                    row_count = stats.get('row_count', 'N/A')
                    print(f"{i:2d}. {collection:<30} (数据量: {row_count})")
                except:
                    print(f"{i:2d}. {collection:<30} (数据量: N/A)")
            else:
                print(f"{i:2d}. {collection}")
        print("-" * 60)
        return collections

    def parse_indices_input(self, input_str: str, max_index: int) -> list:
        """解析用户输入的序号，支持单个、多个、范围"""
        indices = []

        if not input_str.strip():
            return indices

        try:
            # 分割逗号分隔的部分
            parts = input_str.replace(' ', '').split(',')

            for part in parts:
                if '-' in part:
                    # 处理范围，如 "1-3"
                    start, end = map(int, part.split('-'))
                    indices.extend(range(start, end + 1))
                else:
                    # 处理单个数字
                    indices.append(int(part))

            # 去重并排序
            indices = sorted(list(set(indices)))

            # 过滤有效范围
            valid_indices = [i for i in indices if 1 <= i <= max_index]

            return valid_indices

        except ValueError:
            print("❌ 输入格式错误，请使用数字、逗号和连字符")
            return []

def show_menu():
    """显示菜单"""
    print("\n" + "="*70)
    print("🗂️  Milvus知识库管理器 (增强版)")
    print("="*70)
    print("1. 📋 查看所有知识库")
    print("2. ➕ 创建新知识库")
    print("3. ❌ 删除知识库 (支持批量)")
    print("4. 🏷️  重命名知识库")
    print("5. ℹ️  查看知识库详情")
    print("6. 📊 查看知识库统计")
    print("7. 🔄 刷新连接")
    print("0. 🚪 退出程序")
    print("="*70)
    print("💡 删除支持: 单个(3), 多个(1,3,5), 范围(1-3), 混合(1,3-5,7)")

def main():
    """主程序"""
    print("🚀 启动Milvus知识库管理器...")

    # 初始化管理器
    manager = MilvusKBManager()

    while True:
        show_menu()
        choice = input("请选择操作 (0-7): ").strip()

        if choice == "1":
            # 查看所有知识库
            manager.display_collections()

        elif choice == "2":
            # 创建新知识库
            print("\n➕ 创建新知识库")
            name = input("请输入知识库名称 (英文字母/数字/下划线): ").strip()
            if name:
                description = input("请输入描述 (可选): ").strip()
                manager.create_collection(name, description)
            else:
                print("❌ 知识库名称不能为空")

        elif choice == "3":
            # 删除知识库（支持批量）
            print("\n❌ 删除知识库")
            collections = manager.display_collections()
            if collections:
                print("\n💡 输入示例:")
                print("  单个: 3")
                print("  多个: 1,3,5")
                print("  范围: 1-3")
                print("  混合: 1,3-5,7")

                indices_input = input("\n请输入要删除的序号: ").strip()
                if indices_input:
                    indices = manager.parse_indices_input(indices_input, len(collections))
                    if indices:
                        manager.delete_collection_by_index(indices)
                    else:
                        print("❌ 没有有效的序号")
                else:
                    print("❌ 序号不能为空")

        elif choice == "4":
            # 重命名知识库
            print("\n🏷️  重命名知识库")
            collections = manager.display_collections()
            if collections:
                try:
                    index = int(input("请输入要重命名的知识库序号: ").strip())
                    if 1 <= index <= len(collections):
                        old_name = collections[index - 1]
                        new_name = input(f"请输入新名称 (当前: {old_name}): ").strip()
                        if new_name:
                            manager.rename_collection(old_name, new_name)
                        else:
                            print("❌ 新名称不能为空")
                    else:
                        print(f"❌ 序号超出范围 (1-{len(collections)})")
                except ValueError:
                    print("❌ 请输入有效的数字")

        elif choice == "5":
            # 查看知识库详情
            print("\nℹ️  查看知识库详情")
            collections = manager.display_collections()
            if collections:
                try:
                    index = int(input("请输入知识库序号: ").strip())
                    if 1 <= index <= len(collections):
                        name = collections[index - 1]
                        info = manager.get_collection_info(name)
                        if info:
                            print(f"\n📊 知识库 '{name}' 详情:")
                            print(f"   统计信息: {info['stats']}")
                    else:
                        print(f"❌ 序号超出范围 (1-{len(collections)})")
                except ValueError:
                    print("❌ 请输入有效的数字")

        elif choice == "6":
            # 查看知识库统计
            print("\n📊 知识库统计信息")
            manager.display_collections(show_stats=True)

        elif choice == "7":
            # 刷新连接
            print("\n🔄 刷新连接...")
            manager = MilvusKBManager()

        elif choice == "0":
            # 退出程序
            print("\n👋 感谢使用Milvus知识库管理器！")
            break

        else:
            print("❌ 无效选择，请输入0-7之间的数字")

        # 等待用户按键继续
        if choice != "0":
            input("\n按回车键继续...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
