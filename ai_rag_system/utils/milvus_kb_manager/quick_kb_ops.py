#!/usr/bin/env python3
"""
Milvus知识库快捷操作脚本
支持命令行参数快速操作
"""
import os
import sys
import argparse
from dotenv import load_dotenv

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取ai_rag_system目录 (当前目录的上上级)
ai_rag_dir = os.path.dirname(os.path.dirname(current_dir))

# 加载环境变量
env_path = os.path.join(ai_rag_dir, '.env')
load_dotenv(env_path)

from pymilvus import MilvusClient

def get_client():
    """获取Milvus客户端"""
    uri = os.getenv('MILVUS_URI')
    if not uri:
        print("❌ 错误：未找到MILVUS_URI环境变量")
        sys.exit(1)
    return MilvusClient(uri=uri)

def list_kb():
    """列出所有知识库"""
    try:
        client = get_client()
        collections = client.list_collections()
        
        if not collections:
            print("📭 当前没有任何知识库")
            return
        
        print(f"📚 知识库列表 (共{len(collections)}个):")
        for i, kb in enumerate(collections, 1):
            print(f"  {i}. {kb}")
            
    except Exception as e:
        print(f"❌ 操作失败: {e}")

def create_kb(name, force=False):
    """创建知识库"""
    try:
        client = get_client()
        collections = client.list_collections()
        
        if name in collections:
            if not force:
                print(f"⚠️  知识库 '{name}' 已存在")
                return
            else:
                print(f"🔄 知识库 '{name}' 已存在，强制重新创建...")
                client.drop_collection(collection_name=name)
        
        client.create_collection(
            collection_name=name,
            dimension=1536,
            metric_type="COSINE",
            consistency_level="Strong"
        )
        
        print(f"✅ 成功创建知识库: {name}")
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")

def delete_kb(name, force=False):
    """删除知识库"""
    try:
        client = get_client()
        collections = client.list_collections()
        
        if name not in collections:
            print(f"⚠️  知识库 '{name}' 不存在")
            return
        
        if not force:
            confirm = input(f"⚠️  确定要删除知识库 '{name}' 吗？(y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 取消删除")
                return
        
        client.drop_collection(collection_name=name)
        print(f"✅ 成功删除知识库: {name}")
        
    except Exception as e:
        print(f"❌ 删除失败: {e}")

def clear_all_kb(force=False):
    """清空所有知识库"""
    try:
        client = get_client()
        collections = client.list_collections()
        
        if not collections:
            print("📭 当前没有任何知识库")
            return
        
        print(f"⚠️  将要删除 {len(collections)} 个知识库:")
        for kb in collections:
            print(f"  - {kb}")
        
        if not force:
            confirm = input("确定要删除所有知识库吗？(y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 取消操作")
                return
        
        for kb in collections:
            client.drop_collection(collection_name=kb)
            print(f"✅ 删除: {kb}")
        
        print("🎉 所有知识库已清空")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")

def create_demo_kb():
    """创建演示知识库"""
    demo_kbs = [
        "software_testing_kb",
        "python_programming_kb", 
        "ai_technology_kb",
        "web_development_kb"
    ]
    
    print("🎭 创建演示知识库...")
    for kb in demo_kbs:
        create_kb(kb, force=True)
    
    print("🎉 演示知识库创建完成！")

def rename_kb(old_name, new_name, force=False):
    """重命名知识库"""
    try:
        client = get_client()
        collections = client.list_collections()
        
        if old_name not in collections:
            print(f"⚠️  源知识库 '{old_name}' 不存在")
            return
        
        if new_name in collections:
            print(f"⚠️  目标名称 '{new_name}' 已存在")
            return
        
        if not force:
            confirm = input(f"⚠️  确定要重命名 '{old_name}' -> '{new_name}' 吗？(y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 取消重命名")
                return
        
        print(f"🔄 开始重命名: {old_name} -> {new_name}")
        
        # 创建新集合
        client.create_collection(
            collection_name=new_name,
            dimension=1536,
            metric_type="COSINE",
            consistency_level="Strong"
        )
        
        # 尝试复制数据
        try:
            search_results = client.query(
                collection_name=old_name,
                filter="",
                output_fields=["*"],
                limit=10000
            )
            
            if search_results:
                client.insert(collection_name=new_name, data=search_results)
                print(f"✅ 数据复制完成: {len(search_results)} 条")
        except Exception as e:
            print(f"⚠️  数据复制失败: {e}")
        
        # 删除原集合
        client.drop_collection(collection_name=old_name)
        print(f"✅ 重命名完成: {old_name} -> {new_name}")
        
    except Exception as e:
        print(f"❌ 重命名失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="Milvus知识库快捷操作工具")
    parser.add_argument('action', choices=['list', 'create', 'delete', 'rename', 'clear', 'demo'], 
                       help='操作类型')
    parser.add_argument('--name', '-n', help='知识库名称')
    parser.add_argument('--new-name', help='新的知识库名称（重命名时使用）')
    parser.add_argument('--force', '-f', action='store_true', help='强制执行，不询问确认')
    
    args = parser.parse_args()
    
    if args.action == 'list':
        list_kb()
        
    elif args.action == 'create':
        if not args.name:
            print("❌ 创建知识库需要指定名称 (--name)")
            return
        create_kb(args.name, args.force)
        
    elif args.action == 'delete':
        if not args.name:
            print("❌ 删除知识库需要指定名称 (--name)")
            return
        delete_kb(args.name, args.force)
        
    elif args.action == 'rename':
        if not args.name or not args.new_name:
            print("❌ 重命名需要指定原名称 (--name) 和新名称 (--new-name)")
            return
        rename_kb(args.name, args.new_name, args.force)
        
    elif args.action == 'clear':
        clear_all_kb(args.force)
        
    elif args.action == 'demo':
        create_demo_kb()

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 没有参数时显示帮助
        print("🗂️  Milvus知识库快捷操作工具 (增强版)")
        print("\n使用示例:")
        print("  python quick_kb_ops.py list                              # 列出所有知识库")
        print("  python quick_kb_ops.py create --name my_kb               # 创建知识库")
        print("  python quick_kb_ops.py delete --name my_kb               # 删除知识库")
        print("  python quick_kb_ops.py rename --name old_kb --new-name new_kb  # 重命名知识库")
        print("  python quick_kb_ops.py clear                             # 清空所有知识库")
        print("  python quick_kb_ops.py demo                              # 创建演示知识库")
        print("\n选项:")
        print("  --force, -f      强制执行，不询问确认")
        print("  --name, -n       指定知识库名称")
        print("  --new-name       指定新的知识库名称（重命名时使用）")
        print("\n运行完整管理器:")
        print("  python milvus_kb_manager.py")
    else:
        main()
