#!/usr/bin/env python3
"""
知识库管理器启动脚本
从任何位置启动知识库管理器
"""
import os
import sys
import subprocess

def main():
    """启动知识库管理器"""
    # 获取当前脚本的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 获取管理器脚本路径
    manager_script = os.path.join(current_dir, 'milvus_kb_manager.py')
    
    if not os.path.exists(manager_script):
        print("❌ 找不到管理器脚本")
        print(f"   期望路径: {manager_script}")
        return
    
    print("🚀 启动Milvus知识库管理器...")
    print(f"📁 脚本位置: {manager_script}")
    print()
    
    # 启动管理器
    try:
        subprocess.run([sys.executable, manager_script], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")

if __name__ == "__main__":
    main()
