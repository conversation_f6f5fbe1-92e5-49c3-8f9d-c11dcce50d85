@echo off
chcp 65001 >nul
echo 🗂️  Milvus知识库管理器 (增强版)
echo.

if "%1"=="" goto :menu
if "%1"=="list" goto :list
if "%1"=="create" goto :create
if "%1"=="delete" goto :delete
if "%1"=="rename" goto :rename
if "%1"=="clear" goto :clear
if "%1"=="demo" goto :demo
if "%1"=="manager" goto :manager
goto :help

:menu
echo 请选择操作:
echo 1. 查看所有知识库
echo 2. 创建知识库
echo 3. 删除知识库
echo 4. 重命名知识库
echo 5. 清空所有知识库
echo 6. 创建演示知识库
echo 7. 打开完整管理器
echo 0. 退出
echo.
set /p choice="请输入选择 (0-7): "

if "%choice%"=="1" goto :list
if "%choice%"=="2" goto :create_interactive
if "%choice%"=="3" goto :delete_interactive
if "%choice%"=="4" goto :rename_interactive
if "%choice%"=="5" goto :clear
if "%choice%"=="6" goto :demo
if "%choice%"=="7" goto :manager
if "%choice%"=="0" goto :exit
echo 无效选择
goto :menu

:list
python quick_kb_ops.py list
goto :end

:create
if "%2"=="" (
    echo ❌ 请指定知识库名称
    echo 用法: kb_manager.bat create 知识库名称
    goto :end
)
python quick_kb_ops.py create --name %2
goto :end

:create_interactive
set /p name="请输入知识库名称: "
if "%name%"=="" (
    echo ❌ 知识库名称不能为空
    goto :menu
)
python quick_kb_ops.py create --name %name%
goto :menu

:delete
if "%2"=="" (
    echo ❌ 请指定知识库名称
    echo 用法: kb_manager.bat delete 知识库名称
    goto :end
)
python quick_kb_ops.py delete --name %2
goto :end

:delete_interactive
python quick_kb_ops.py list
echo.
set /p name="请输入要删除的知识库名称: "
if "%name%"=="" (
    echo ❌ 知识库名称不能为空
    goto :menu
)
python quick_kb_ops.py delete --name %name%
goto :menu

:rename
if "%2"=="" (
    echo ❌ 请指定原知识库名称和新名称
    echo 用法: kb_manager.bat rename 原名称 新名称
    goto :end
)
if "%3"=="" (
    echo ❌ 请指定新知识库名称
    echo 用法: kb_manager.bat rename 原名称 新名称
    goto :end
)
python quick_kb_ops.py rename --name %2 --new-name %3
goto :end

:rename_interactive
python quick_kb_ops.py list
echo.
set /p old_name="请输入要重命名的知识库名称: "
if "%old_name%"=="" (
    echo ❌ 原名称不能为空
    goto :menu
)
set /p new_name="请输入新的知识库名称: "
if "%new_name%"=="" (
    echo ❌ 新名称不能为空
    goto :menu
)
python quick_kb_ops.py rename --name %old_name% --new-name %new_name%
goto :menu

:clear
python quick_kb_ops.py clear
goto :end

:demo
python quick_kb_ops.py demo
goto :end

:manager
python milvus_kb_manager.py
goto :end

:help
echo 🗂️  Milvus知识库管理器 (增强版)
echo.
echo 用法:
echo   kb_manager.bat                         # 交互式菜单
echo   kb_manager.bat list                    # 列出所有知识库
echo   kb_manager.bat create 名称             # 创建知识库
echo   kb_manager.bat delete 名称             # 删除知识库
echo   kb_manager.bat rename 原名称 新名称     # 重命名知识库
echo   kb_manager.bat clear                   # 清空所有知识库
echo   kb_manager.bat demo                    # 创建演示知识库
echo   kb_manager.bat manager                 # 打开完整管理器
echo.
goto :end

:exit
echo 👋 再见！
goto :end

:end
if "%1"=="" (
    echo.
    pause
    goto :menu
)
