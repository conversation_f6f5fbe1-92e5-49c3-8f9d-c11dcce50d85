# 🗂️ Milvus知识库管理工具

专业的Milvus向量数据库知识库管理工具集，提供完整的知识库生命周期管理功能。

## 📁 文件结构

```
kb_manager/
├── __init__.py                 # 包初始化文件
├── milvus_kb_manager.py        # 完整管理器 (交互式界面)
├── quick_kb_ops.py             # 快捷操作脚本 (命令行)
├── kb_manager.bat              # Windows批处理脚本
├── start_manager.py            # 启动脚本
└── README.md                   # 本文档
```

## 🚀 快速开始

### 方式1: 完整管理器（推荐新手）
```bash
python milvus_kb_manager.py
```

### 方式2: 快捷命令行操作
```bash
# 查看所有知识库
python quick_kb_ops.py list

# 创建知识库
python quick_kb_ops.py create --name my_kb

# 删除知识库
python quick_kb_ops.py delete --name my_kb

# 重命名知识库
python quick_kb_ops.py rename --name old_kb --new-name new_kb
```

### 方式3: Windows批处理
```cmd
# 交互式菜单
kb_manager.bat

# 直接操作
kb_manager.bat list
kb_manager.bat create my_kb
```

## ✨ 主要功能

### 🎯 完整管理器功能
- 📋 查看所有知识库
- ➕ 创建新知识库
- ❌ 删除知识库 (支持批量删除)
- 🏷️ 重命名知识库
- ℹ️ 查看知识库详情
- 📊 查看知识库统计信息
- 🔄 刷新连接

### ⚡ 批量操作支持
- **单个删除**: `3`
- **多个删除**: `1,3,5`
- **范围删除**: `1-3`
- **混合删除**: `1,3-5,7`

### 🔧 命令行操作
- `list` - 列出所有知识库
- `create` - 创建新知识库
- `delete` - 删除知识库
- `rename` - 重命名知识库
- `clear` - 清空所有知识库
- `demo` - 创建演示知识库

## 📝 使用示例

### 创建知识库
```bash
# 基本创建
python quick_kb_ops.py create --name software_testing_kb

# 强制创建（覆盖已存在的）
python quick_kb_ops.py create --name my_kb --force
```

### 批量删除知识库
```bash
# 启动完整管理器
python milvus_kb_manager.py
# 选择 3. 删除知识库
# 输入: 1,3,5  (删除第1、3、5个知识库)
# 输入: 1-3    (删除第1到3个知识库)
# 输入: 1,3-5,7 (混合方式删除)
```

### 重命名知识库
```bash
# 命令行重命名
python quick_kb_ops.py rename --name old_name --new-name new_name

# 强制重命名（不询问确认）
python quick_kb_ops.py rename --name old_name --new-name new_name --force
```

### 创建演示数据
```bash
# 一键创建4个演示知识库
python quick_kb_ops.py demo
```

## 🔧 配置要求

### 环境变量
确保在 `ai_rag_system/.env` 文件中配置了以下变量：
```env
MILVUS_URI=http://your-milvus-server:19530
```

### 依赖库
```bash
pip install pymilvus python-dotenv
```

## 📊 知识库命名规则

✅ **有效名称**:
- `my_knowledge_base`
- `software_testing_kb`
- `python_programming`
- `_private_kb`

❌ **无效名称**:
- `我的知识库` (不能用中文)
- `123_kb` (不能以数字开头)
- `my-kb` (不能用连字符)
- `my kb` (不能有空格)

## 🛠️ 故障排除

### 连接失败
```
❌ 连接Milvus失败
```
**解决方案**:
1. 检查 `MILVUS_URI` 环境变量
2. 确认Milvus服务正在运行
3. 检查网络连接

### 权限错误
```
❌ 操作失败: permission denied
```
**解决方案**:
1. 检查Milvus服务器权限设置
2. 确认连接用户有相应操作权限

### 名称无效
```
❌ 无效的集合名称
```
**解决方案**:
1. 使用英文字母、数字、下划线
2. 以字母或下划线开头
3. 避免特殊字符和空格

## 💡 最佳实践

1. **命名规范**: 使用描述性的英文名称
2. **定期清理**: 删除不用的测试知识库
3. **数据备份**: 重要操作前先备份数据
4. **批量操作**: 使用批量删除提高效率

## 📞 技术支持

如果遇到问题，请检查：
1. Python环境是否正确
2. 依赖库是否安装完整
3. 环境变量是否配置正确
4. Milvus服务是否正常运行

---

🎉 现在你可以高效地管理Milvus知识库了！
