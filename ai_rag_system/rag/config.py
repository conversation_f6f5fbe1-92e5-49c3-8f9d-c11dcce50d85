import os
from pydantic import BaseModel, Field


class RAGConfig(BaseModel):
    milvus_uri: str = Field(default=os.environ.get("MILVUS_URI"),description="Milvus服务器连接URI")

    embedding_model_dim: int = Field(default=512,description="嵌入模型维度")

    moonshot_api_key: str = Field(default=os.environ.get("MOONSHOT_API_KEY"),description="Moonshot API Key")
    deepseek_api_key: str = Field(default=os.environ.get("GITEE_API_KEY"),description="Gitee API Key")
    kimi_api_key: str = Field(default=os.environ.get("GITEE_API_KEY"), description="Gitee API Key")
    fastgpt_api_key: str = Field(default=os.environ.get("FASTGPT_API_KEY"),description="FastGPT API Key")

    ocr_base_url: str = Field(default="http://**************:1224",description="OCR服务地址")
    ocr_download_dir: str = Field(default="../ocr_download",description="OCR文件保存目录")


RagConfig = RAGConfig()